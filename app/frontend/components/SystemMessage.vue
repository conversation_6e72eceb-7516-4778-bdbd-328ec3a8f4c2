<!-- ABOUTME: System-wide dismissible message component for announcements and notifications
ABOUTME: Messages are hardcoded and updated via deployment, dismissed state stored in localStorage -->
<template>
  <div v-if="visibleMessages.length > 0" class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4 max-w-[500px]">
    <div class="flex items-start">
      <div class="flex-shrink-0 mr-3">
        <Megaphone class="text-blue-600" :size="20" />
      </div>
      <div class="flex-1">
        <div class="flex items-center mb-2">
          <span class="text-blue-700 font-bold text-lg">{{ $t('announcements', 'Oznámení') }}</span>
        </div>
        <div v-show="!isAnnouncementsCollapsed">
          <div
            v-for="(message, index) in visibleMessages"
            :key="message.id"
            :class="['announcement-item', { 'border-t border-gray-200 pt-3 mt-3': index > 0 }]"
            :data-testid="`system-message-${message.id}`"
          >
            <div>
              <h4 v-if="message.title" class="text-gray-800 font-semibold mb-2">
                {{ message.title }}
              </h4>

              <!-- Simple bullet list for announcements -->
              <ul v-if="message.items" class="text-gray-700 text-sm space-y-1">
                <li v-for="(item, itemIndex) in message.items" :key="itemIndex" class="list-disc">
                  <strong>{{ item.title }}</strong> - {{ item.description }}
                </li>
              </ul>
              <!-- Fallback for simple text content -->
              <div v-else class="text-gray-700 text-sm">
                {{ message.content }}
              </div>

              <div v-if="message.dismissible !== false" class="flex mt-3 justify-end">
                <button
                  @click="dismissMessage(message.id)"
                  class="btn btn-small btn-light"
                  :data-testid="`dismiss-${message.id}`"
                >
                  {{ $t('announcement_dismiss', 'Přečetl jsem si') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button @click="toggleAnnouncements" class="ml-2 text-gray-500 hover:text-gray-700">
        <ChevronUp v-if="!isAnnouncementsCollapsed" :size="18" />
        <ChevronDown v-else :size="18" />
      </button>
    </div>
  </div>
</template>

<script>
import { Megaphone, ChevronUp, ChevronDown } from 'lucide-vue-next';

export default {
  name: 'SystemMessage',
  components: {
    Megaphone,
    ChevronUp,
    ChevronDown
  },
  data() {
    return {
      isAnnouncementsCollapsed: false,
      // DEPLOYMENT MESSAGES: Update these messages and deploy to show new announcements
      // Each message needs a unique ID that should never be reused
      // Format: type-yyyy-mm-dd-sequence (e.g., 'feature-2024-01-10-01')
      messages: [
        // Example of a multiline version announcement:
        {
          id: 'version-2025-08-10',
          title: 'Nová verze 2.5.0',
          items: [
            { title: 'Vylepšené PWA', description: 'Aplikace nyní funguje offline a lze ji nainstalovat na plochu' },
            { title: 'Rychlejší načítání', description: 'Optimalizované cachování pro 2x rychlejší odezvu' },
            { title: 'Nový design', description: 'Modernější vzhled s lepším kontrastem a čitelností' },
            { title: 'Opravy chyb', description: 'Vyřešeno 15 nahlášených problémů' }
          ],
          dismissible: true
        },
        {
          id: 'maintenance-2025-01-15',
          title: 'Plánovaná údržba',
          content: 'Systém bude 15.1.2025 od 22:00 do 23:00 v režimu údržby. Děkujeme za pochopení.',
          dismissible: true
        }
      ]
    };
  },
  computed: {
    visibleMessages() {
      const dismissed = this.getDismissedMessages();
      return this.messages.filter(msg => !dismissed.includes(msg.id));
    }
  },
  methods: {
    getDismissedMessages() {
      try {
        const stored = localStorage.getItem('dismissed_system_messages');
        if (!stored) return [];
        const parsed = JSON.parse(stored);
        return Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        console.warn('Could not parse dismissed messages:', e);
        return [];
      }
    },
    
    dismissMessage(messageId) {
      let dismissed = this.getDismissedMessages();
      
      // Add the new dismissed message ID
      if (!dismissed.includes(messageId)) {
        dismissed.push(messageId);
      }
      
      // Cleanup: Keep only the last 30 dismissed message IDs to prevent unlimited growth
      if (dismissed.length > 30) {
        dismissed = dismissed.slice(-30);
      }
      
      // Save to localStorage
      try {
        localStorage.setItem('dismissed_system_messages', JSON.stringify(dismissed));
        // Force re-render by triggering computed property
        this.$forceUpdate();
      } catch (e) {
        console.error('Could not save dismissed message:', e);
      }
    },
    
    toggleAnnouncements() {
      this.isAnnouncementsCollapsed = !this.isAnnouncementsCollapsed;
      localStorage.setItem('system_announcements_collapsed', JSON.stringify(this.isAnnouncementsCollapsed));
    },
    

    
    cleanupOldDismissals() {
      try {
        const dismissed = this.getDismissedMessages();
        const currentMessageIds = this.messages.map(m => m.id);
        
        // Remove dismissed IDs that are no longer in the current messages list
        // and keep last 20 dismissed IDs that might be for future messages
        const relevant = dismissed.filter(id => {
          const index = dismissed.indexOf(id);
          const isRecent = index >= dismissed.length - 20;
          const isCurrent = currentMessageIds.includes(id);
          return isCurrent || isRecent;
        });
        
        if (relevant.length !== dismissed.length) {
          localStorage.setItem('dismissed_system_messages', JSON.stringify(relevant));
        }
      } catch (e) {
        console.warn('Could not cleanup old dismissals:', e);
      }
    }
  },
  mounted() {
    // Load collapsed state from localStorage
    const storedCollapsed = localStorage.getItem('system_announcements_collapsed');
    if (storedCollapsed !== null) {
      try {
        this.isAnnouncementsCollapsed = JSON.parse(storedCollapsed);
      } catch (e) {
        this.isAnnouncementsCollapsed = false;
      }
    }
    
    // Optional: Clean up very old dismissed message IDs on component mount
    // This runs once per session to keep localStorage tidy
    this.cleanupOldDismissals();
  }
};
</script>

<style scoped>
/* Keep styles minimal - mostly using Tailwind classes */
.announcement-item {
  transition: all 0.2s ease;
}

/* Proper bullet point styling */
.announcement-item ul {
  list-style-type: disc;
  padding-left: 1.5rem;
}

.announcement-item li {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}
</style>